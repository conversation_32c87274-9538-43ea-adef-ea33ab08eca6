###############################################################
# initialisation
###############################################################

# clean up environment
rm(list = ls())

# load packages
library(dplyr)
library(tidyr)
library(terra)
library(rstan)
options(mc.cores = parallel::detectCores())
library(ineq)
library(shinystan)
library(here)

# load function to extract chain quantiles
source(here('R/tree_cover.r'))

# load tree data
tree <- readRDS(here('./data/treeDB.RDS')) %>%
        filter(dbh >= 10) %>% # TODO: carefull! it removes trees with no dbh and htot which might affect the final results (?)
        filter(htot >= 5)

# load plot map
map <- vect(here('./data/sampling.shp'))

###############################################################
# calculate tree cover
###############################################################

# calculate [m]ean [r]adius
tree <- tree %>%
        mutate(mr = rowMeans(across(cN:cNO), na.rm = TRUE))

# TODO: try with new coord ?
# TODO: manage trees with no crowns (their crowns should be modeled?: check their number)
# TODO: manage trees with no coordinates...
tree <- tree %>%
        filter(!is.na(mr)) %>%
        filter(mr>0) %>%
        filter(!is.na(long), !is.na(lat))

# if radius is NA, use mean of the 2 nearest radii
tree <- tree %>%
  mutate(
    cN  = if_else(is.na(cN),
                  rowMeans(across(c(cNO, cNE)), na.rm = TRUE),
                  cN),
    cE  = if_else(is.na(cE),
                  rowMeans(across(c(cNE, cSE)), na.rm = TRUE),
                  cE),
    cS  = if_else(is.na(cS),
                  rowMeans(across(c(cSE, cSO)), na.rm = TRUE),
                  cS),
    cO  = if_else(is.na(cO),
                  rowMeans(across(c(cSO, cNO)), na.rm = TRUE),
                  cO),
    cNE = if_else(is.na(cNE),
                  rowMeans(across(c(cN,  cE)), na.rm = TRUE),
                  cNE),
    cSE = if_else(is.na(cSE),
                  rowMeans(across(c(cS,  cE)), na.rm = TRUE),
                  cSE),
    cSO = if_else(is.na(cSO),
                  rowMeans(across(c(cS,  cO)), na.rm = TRUE),
                  cSO),
    cNO = if_else(is.na(cNO),
                  rowMeans(across(c(cN,  cO)), na.rm = TRUE),
                  cNO)
  )

df <- tree_cover(tree, map)

###############################################################
# calculate dendro variables
###############################################################

dendro <- tree %>%
        mutate(ba = pi*((dbh/100)^2)/4,
                dbh2 = dbh^2) %>%
        group_by(clus, type) %>%
        summarise(n = n(),
                  BA = sum(ba),
                  mean_dbh = mean(dbh),
                  med_dbh = median(dbh),
                  dg = sqrt( sum(dbh2) / sum(n) ),
                  gini = Gini(dbh))

# merge dendro and cover data
df <- full_join(df, dendro, join_by(clus, type)) %>%
        mutate(n_ha = 10000 * n / pl_surf,
                ba_ha = 10000 * BA / pl_surf)

###############################################################
# stan model
###############################################################

# model
mod <- stan(here('./stan/model.stan'), data = list(
    N = nrow(df),
    cover = df$cover_circle,
    dens = df$n_ha,
    ba = df$ba_ha
    ),
    cores = getOption("mc.cores", 1L),
)

# # parameter preview
# launch_shinystan(mod)

###############################################################
# save model and formated calibration data
###############################################################

# save model
saveRDS(mod, here('./temp/model.rds'))

# save formated calibration data
saveRDS(df, here('./temp/calib_data.rds'))
