###############################################################
# Test script for dg models - subset of models for quick testing
###############################################################

# clean up environment
rm(list = ls())

# Load packages
library(dplyr)
library(tidyr)
library(terra)
library(rstan)
options(mc.cores = parallel::detectCores())
library(ineq)
library(shinystan)
library(here)
library(ggplot2)
library(sf)
library(ggforce)

# load function to extract chain quantiles
source(here('R/tree_cover.r'))

# load tree data
tree <- readRDS(here('./data/treeDB.RDS')) %>%
        filter(dbh >= 10)

# load plot map
map <- vect(here('./data/sampling.shp'))

# calculate [m]ean [r]adius
tree <- tree %>%
        mutate(mr = rowMeans(across(cN:cNO), na.rm = TRUE))

tree <- tree %>%
        filter(!is.na(mr)) %>%
        filter(mr>0) %>%
        filter(!is.na(long), !is.na(lat))

# Fill missing radii function (simplified version)
fill_missing_radii <- function(tree) {
  tree %>%
    group_by(clus, type) %>%
    mutate(across(c(cE, cNE, cN, cNO, cO, cSO, cS, cSE), 
                  ~ ifelse(is.na(.), mean(., na.rm = TRUE), .))) %>%
    ungroup()
}

# Apply the function twice to fill missing radii
tree <- fill_missing_radii(tree)
tree <- fill_missing_radii(tree)

# calculate tree cover
df <- tree_cover(tree, map)

# calculate dendro variables
dendro <- tree %>%
        mutate(ba = pi*((dbh/100)^2)/4,
                dbh2 = dbh^2) %>%
        group_by(clus, type) %>%
        summarise(n = n(),
                  BA = sum(ba),
                  mean_dbh = mean(dbh),
                  med_dbh = median(dbh),
                  dg = sqrt( sum(dbh2) / sum(n) ),
                  gini = Gini(dbh))

# merge dendro and cover data
df <- full_join(df, dendro, join_by(clus, type)) %>%
        mutate(n_ha = 10000 * n / pl_surf,
                ba_ha = 10000 * BA / pl_surf)

# Compile the generic Stan model once
sm <- stan_model(here('stan', 'multiple_models.stan'))

# Define a subset of model formulas for testing
formulas <- list(
  m01    = ~ 1 + n_ha,                    # original model
  m10    = ~ 1 + log(n_ha + 1) + sqrt(ba_ha),  # best original model
  m12    = ~ 1 + dg,                      # dg only
  m13    = ~ 1 + log(dg),                 # log(dg) only
  m17    = ~ 1 + n_ha + ba_ha + dg,       # all three variables
  m24    = ~ 1 + log(n_ha + 1) + sqrt(ba_ha) + log(dg)  # complex model
)

# Create prediction grids
pred_data_grid_2d <- expand.grid(
  n_ha = seq(0, 160, by = 10),    # reduced resolution for testing
  ba_ha = seq(0, 30, by = 2)      # reduced resolution for testing
)

pred_data_grid_3d <- expand.grid(
  n_ha = seq(0, 160, by = 20),    # reduced resolution for testing
  ba_ha = seq(0, 30, by = 5),     # reduced resolution for testing
  dg = seq(20, 100, by = 20)      # reduced resolution for testing
)

pred_data_grid_dg <- data.frame(
  dg = seq(15, 105, by = 5)       # reduced resolution for testing
)

# Initialize a list to store fitted models
fits <- list()

# Loop through each model to fit and get predictions from Stan
for (model_name in names(formulas)) {
  cat("Fitting model:", model_name, "\n")
  f <- formulas[[model_name]]

  # Prepare original data for Stan
  X_obs <- model.matrix(f, data = df)
  data_list_obs <- list(
    N = nrow(df),
    K = ncol(X_obs),
    X = X_obs,
    cover = df$cover_ellipse
  )

  # Select appropriate prediction grid based on model variables
  formula_vars <- all.vars(f)
  
  if ("dg" %in% formula_vars && ("n_ha" %in% formula_vars || "ba_ha" %in% formula_vars)) {
    # Models with dg + other variables: use 3D grid
    current_pred_grid <- pred_data_grid_3d
  } else if ("dg" %in% formula_vars) {
    # Models with only dg: use 1D dg grid
    current_pred_grid <- pred_data_grid_dg
  } else {
    # Models with only n_ha and/or ba_ha: use 2D grid
    current_pred_grid <- pred_data_grid_2d
  }
  
  # Prepare prediction data for Stan
  X_pred <- model.matrix(f, data = current_pred_grid)
  
  # Ensure K (number of predictors) is consistent
  if (ncol(X_pred) != ncol(X_obs)) {
    stop(paste("Mismatch in number of predictors (K) for model", model_name, 
               "between observed data and prediction data. K_obs:", ncol(X_obs), 
               "K_pred:", ncol(X_pred)))
  }

  # create data list for prediction
  data_list_pred <- list(
    N_pred = nrow(X_pred),
    X_pred = X_pred
  )

  # Combine observed and prediction data for Stan
  full_data_list <- c(data_list_obs, data_list_pred)

  # Fit the model with the combined data
  current_fit <- rstan::sampling(
    sm,
    data    = full_data_list,
    iter    = 1000,  # reduced for testing
    chains  = 2      # reduced for testing
  )
  fits[[model_name]] <- current_fit
}

# save test results
saveRDS(fits, here('temp', 'test_dg_models.rds'))

# save test prediction grids
pred_grids_test <- list(
  grid_2d = pred_data_grid_2d,
  grid_3d = pred_data_grid_3d,
  grid_dg = pred_data_grid_dg
)
saveRDS(pred_grids_test, here('temp', 'test_pred_data.rds'))

cat("Test completed successfully!\n")
