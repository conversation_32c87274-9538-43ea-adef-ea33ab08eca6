data {
  int<lower=1>    N;            // number of observations
  int<lower=1>    K;            // number of predictors (including intercept)
  matrix[N, K]    X;            // design matrix (col 1 = ones for intercept)
  vector<lower=0,upper=1>[N] cover;  // cover in (0,1)

  // New data for predictions
  int<lower=0>    N_pred;       // number of prediction data points
  matrix[N_pred, K] X_pred;     // design matrix for prediction
}

parameters {
  vector[K]     theta;         // regression coefficients
  real<lower=0> phi;           // precision parameter
}

transformed parameters {
  vector[N] mu;
  mu = inv_logit(X * theta);  // logit link
}

model {
  // // priors (customize as needed)
  // beta ~ normal(0, 5);
  // phi  ~ gamma(1, 0.1);

  // beta-likelihood
  cover ~ beta(mu * phi, (1 - mu) * phi);
}

generated quantities {
  // vector[N] log_lik;    // pointwise log-likelihood for loo
  vector[N] pred;       // fitted values (mu)
  real      rmse;       // root mean squared error
  real      ss_res;     // residual sum of squares
  real      ss_tot;     // total sum of squares
  real      R2;         // classical R²
  real      msd;        // mean squared deviation
  real      sb;         // squared bias
  vector[N] xn;         // centered observed values
  vector[N] yn;         // centered fitted values
  real      b;          // slope from regression of pred on obs
  real      r2;         // alternative R² via inner product
  real      nu;         // variance‐due‐to‐unexplained slope
  real      lc;         // variance‐due‐to‐lack of fit

  // // 0) pointwise log-likelihood for LOO/WAIC
  // for (n in 1:N)
  //   log_lik[n] = beta_lpdf(cover[n] | mu[n] * phi, (1 - mu[n]) * phi);

  // 1) copy out fitted means
  pred = mu;

  // 2) RMSE & classical R²
  rmse   = sqrt(mean(square(cover - pred)));
  ss_res = sum(square(cover - pred));
  ss_tot = sum(square(cover - mean(cover)));
  R2     = 1 - ss_res / ss_tot;

  // 3) decomposition into MSD and squared bias
  msd = ss_res / N;
  sb  = square(mean(cover) - mean(pred));

  // 4) center the vectors
  xn = cover      - mean(cover);
  yn = pred  - mean(pred);

  // 5) slope and alternative R²
  b  = dot_product(xn, yn) / sum(square(xn));
  r2 = square(dot_product(xn, yn))
     / (sum(square(xn)) * sum(square(yn)));

  // 6) variance components: unexplained slope & lack-of-fit
  nu = square(1 - b) * (sum(square(xn)) / N);
  lc = (1 - r2)    * (sum(square(yn)) / N);

  // Predictions for new data
  vector[N_pred] mu_pred;     // fitted values for prediction data
  mu_pred = inv_logit(X_pred * theta);
}
