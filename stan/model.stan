data {

    int N ;
    vector <lower = 0, upper = 1> [N] cover ;
    vector<lower=0>[N] dens;
    vector<lower=0>[N] ba;

}

parameters {

    real <upper = 0> alpha ;
    real <lower = 0> theta_d ;
    real <lower = 0> theta_ba ;
    // real <lower = 0.05, upper = 2> beta_d ;
    // real <lower = 0.05, upper = 2> beta_ba ;
    real <lower = 0> phi ;

}

transformed parameters {

    vector <lower = 0, upper = 1> [N] mu ;
    real eta ;
    for (n in 1:N) {
        // linear predictor
        eta = alpha + theta_d * log(dens[n]+1) + theta_ba * sqrt(ba[n]) ;
        // logit link function
        mu[n] = inv_logit(eta) ;
    }

}

model {

    // beta regression
    cover ~ beta(mu * phi, (1 - mu) * phi) ;

}

generated quantities {
  vector[N] pred1;
  real rmse;
  real ss_res;
  real ss_tot;
  real R2;
  real msd;
  real sb;
  vector[N] xn;
  vector[N] yn;
  real b;
  real r2;
  real nu;
  real lc;

  // copy your predictions
  for (n in 1:N)
    pred1[n] = mu[n];

  // classic RMSE/R2
  rmse   = sqrt( mean( square(cover - pred1) ) );
  ss_res = sum( square(cover - pred1) );
  ss_tot = sum( square(cover - mean(cover)) );
  R2     = 1 - ss_res/ss_tot;

  // bias/decomp stats
  msd = ss_res / N;
  sb  = square(mean(cover) - mean(pred1));

  // centered vectors
  xn = cover  - mean(cover);
  yn = pred1  - mean(pred1);

  // inner‐product / elementwise sum
  b  = dot_product(xn, yn) / sum( square(xn) );
  r2 = square( dot_product(xn, yn) )
     / ( sum(square(xn)) * sum(square(yn)) );

  // use N, not nrow(df)
  nu = square(1 - b) * ( sum(square(xn)) / N );
  lc = (1 - r2)    * ( sum(square(yn)) / N );
}






// generated quantities {

//     // predict cover for observed values and calculate RMSE and R2
//     vector [N] pred1 ;
//     real rmse ;
//     real ss_res ;
//     real ss_tot ;
//     real R2 ;
//     real msd ;
//     real sb ;
//     vector [N] xn ;
//     vector [N] yn ;
//     real b ;
//     real r2 ;
//     real nu ;
//     real lc ;
//     for (n in 1:N){
//         pred1[n] = mu[n] ;
//     }
//     rmse = sqrt(mean(square(cover - pred1))) ;
//     ss_res = sum(square(cover - pred1)) ;
//     ss_tot = sum(square(cover - mean(cover))) ;
//     R2 = 1 - (ss_res / ss_tot) ;
//     msd = sum(square(cover - pred1)) / N ;
//     sb = square(mean(cover) - mean(pred1)) ;
//     xn = cover - mean(cover) ;
//     yn = pred1 - mean(pred1) ;
//     b = sum(xn*yn) / sum(square(xn)) ;
//     r2 = square(sum(xn*yn)) / (sum(square(xn)) * sum(square(yn))) ;
//     nu = square(1-b) * (sum(square(xn)) / nrow(df)) ;
//     lc = (1-r2) * (sum(square(yn)) / nrow(df)) ;

// }

    // // predict cover for a [density ; mean dbh] grid
    // matrix [200, 30] pred ;
    // for (i in 1:200){
    //     for (j in 1:30){
    //         pred[i,j] = inv_logit(alpha + theta_d * pow(i, beta_d) + theta_ba * pow(j, beta_ba)) ;
    //     }
    // }

    // // predict count for observed values and calculate RMSE and R2
    // vector [Nobs] pred_count ;
    // real rmse_count ;
    // real ss_res_count ;
    // real ss_tot_count ;
    // real r2_count ;
    // for (n in 1:Nobs){
    //     pred_count[n] = mu1[n] ;
    // }
    // vector[Nobs] count_vec = to_vector(count) ; // Convert count to a vector for operations
    // rmse_count = sqrt(mean(square(count_vec - pred_count))) ;
    // ss_res_count = sum(square(count_vec - pred_count)) ;
    // ss_tot_count = sum(square(count_vec - mean(count_vec))) ;
    // r2_count = 1 - (ss_res_count / ss_tot_count) ;

    // // Predictions for all combinations of n_ha, dbh_class, and plot_mean_dbh
    // int N_n_ha = 20;            // Number of n_ha values (from 1 to 200 by increments of 10)
    // int N_dbh = 30;              // Number of dbh_class values (from 10 to 300 by increments of 10)
    // int N_plot_mean_dbh = 10;    // Number of plot_mean_dbh values (from 10 to 100 by increments of 10)

    // // Declare a 3D array to store predictions
    // array[20, 30, 10] real pred_counts;

    // // Compute predictions for each combination
    // for (i in 1:N_n_ha) {
    //     real n_ha_val = (i - 1) * 10 + 1;  // n_ha values: 1, 11, 21, ..., 191
    //     for (j in 1:N_dbh) {
    //         real dbh_class_val = (j - 1) * 10 + 10;  // dbh_class values: 10, 20, ..., 80
    //         for (k in 1:N_plot_mean_dbh) {
    //             real plot_mean_dbh_val = (k - 1) * 10 + 10;  // plot_mean_dbh values: 10, 20, ..., 80
    //             real eta_pred = beta0 + beta1 * dbh_class_val + beta2 * log(n_ha_val) + exp(beta3 * fabs(dbh_class_val - plot_mean_dbh_val)) ;
    //             pred_counts[i, j, k] = exp(eta_pred);
    //         }
    //     }
    // }

    // // Predict loss for observed values
    // vector[Nloss] pred_loss;
    // real rmse_loss;
    // real ss_res_loss;
    // real ss_tot_loss;
    // real r2_loss;

    // for (n in 1:Nloss) {
    //     pred_loss[n] = mu_loss[n] ;  // Predicted value of loss
    // }

    // // Calculate RMSE
    // rmse_loss = sqrt(mean(square(mean_loss - pred_loss)));

    // // Calculate R-squared
    // ss_res_loss = sum(square(mean_loss - pred_loss));
    // ss_tot_loss = sum(square(mean_loss - mean(mean_loss)));
    // r2_loss = 1 - (ss_res_loss / ss_tot_loss);
// 
// }
