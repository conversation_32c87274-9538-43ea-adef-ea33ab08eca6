###############################################################
# initialisation
###############################################################

# clean up environment
rm(list = ls())

# Load packages
library(dplyr)
library(tidyr)
library(rstan)
library(ggplot2)
library(here)

# load calibration data
df <- readRDS(here("temp", "multiple_calib_data.RDS"))

# load prediction data grids
pred_grids <- readRDS(here("temp", "multiple_pred_data.rds"))

# load model
mod <- readRDS(here("temp", "multiple_models.rds"))

# load function to extract chain quantiles
source(here("R", "extract_quantiles_multiple_models.r"))

###############################################################
# goodness of fit
###############################################################

# extract goodness of fit index quantiles
good <- extract_good(mod)

# plot R2 ranking
r2 <- ggplot(data = good %>%
         filter(parameter == 'R2') %>%
         arrange(median)) +
    geom_bar(aes(x = reorder(model, median, decreasing = TRUE), y = median), stat = "identity", fill = 'skyblue') +
    geom_errorbar(aes(x = model, ymin = lower_ci, ymax = upper_ci), width=0.4, colour="orange", alpha=0.9) +
    xlab('model') +
    ylab(expression(R^2)) +
    theme_bw()
r2
ggsave(file = here('./output/r2.jpg'), plot = r2, width = 10, height = 10)

# plot RMSE ranking
rmse <- ggplot(data = good %>% 
         filter(parameter == 'rmse') %>%
         arrange(median)) +
    geom_bar(aes(x = reorder(model, median, decreasing = TRUE), y = median), stat = "identity", fill = 'skyblue') +
    geom_errorbar(aes(x = model, ymin = lower_ci, ymax = upper_ci), width=0.4, colour="orange", alpha=0.9) +
    xlab('model') +
    ylab('RMSE') +
    theme_bw()
rmse
ggsave(file = here('./output/rmse.jpg'), plot = rmse, width = 10, height = 10)

# plot MSD ranking
msd <- ggplot(data = good %>% 
         filter(parameter == 'msd') %>%
         arrange(median)) +
    geom_bar(aes(x = reorder(model, median, decreasing = TRUE), y = median), stat = "identity", fill = 'skyblue') +
    geom_errorbar(aes(x = model, ymin = lower_ci, ymax = upper_ci), width=0.4, colour="orange", alpha=0.9) +
    xlab('model') +
    ylab('MSD') +
    theme_bw()
msd
ggsave(file = here('./output/msd.jpg'), plot = msd, width = 10, height = 10)

# plot MSD with components (SB, LC, NU)
# first manage factor levels
good_msd <- good %>%
    mutate(model = factor(model, levels = good %>% 
                         filter(parameter == 'msd') %>% 
                         arrange(desc(median)) %>% 
                         pull(model)))
good_msd <- good_msd %>%
    filter(parameter %in% c('lc', 'nu', 'sb')) %>%
    mutate(parameter = factor(parameter, levels = c('sb', 'nu', 'lc')))
# plot msd components
msd2 <- ggplot() +
    geom_bar(data = good_msd, aes(x = model, y = median, fill = parameter), stat = "identity", position = "stack") +
    scale_fill_brewer(palette = "Blues", direction = -1) +
    # geom_bar(data = good %>% filter(parameter == 'msd'), aes(x = model, y = median), stat = "identity", position = 'dodge', alpha = 0.2) +
    xlab('model') +
    ylab('MSD components') +
    theme_bw()
msd2
ggsave(file = here('./output/msd_components.jpg'), plot = msd2, width = 10, height = 10)

###############################################################
# plot obs vs predicted
###############################################################

# extract pred quantiles
pred <- extract_pred(mod)

# merge with df
# (df must first be duplicated to match the number of rows in pred = for each model)
# 1) stretch df to 450 rows
df_rep <- df %>% 
  slice(rep(row_number(), length.out = nrow(pred)))
# 2) bind them side–by–side
df_rep <- bind_cols(df_rep, pred)

# plot obs vs pred with R2 values
pl1 <- ggplot(data = df_rep) +
    geom_point(aes(x = cover_ellipse, y = median), alpha = 0.5) +
    geom_abline(slope = 1, intercept = 0, linetype = "dashed", color = "red") +
    # geom_text(data = good %>% filter(parameter == 'R2'), aes(x = 0.25, y = 0.9, label = paste0("R² = ", round(median, 3)))) +
    # geom_text(data = good %>% filter(parameter == 'rmse'), aes(x = 0.25, y = 0.8, label = paste0("RMSE = ", round(median, 3)))) +
    # geom_text(data = good %>% filter(parameter == 'msd'), aes(x = 0.25, y = 0.7, label = paste0("msd = ", round(median, 3)))) +
    facet_wrap(.~ model) +
    coord_fixed(ratio = 1) +
    theme_bw()
pl1
ggsave(file = here('./output/multiple_obs_vs_pred.jpg'), plot = pl1, width = 10, height = 10)

###############################################################
# cover diagram
###############################################################

# extract mu_pred quantiles
mu_pred <- extract_mu_pred(mod, pred_grids)

# plot cover diagram for 2D models only (models with n_ha and ba_ha)
mu_pred_2d <- mu_pred %>%
  filter(model %in% paste0("m", sprintf("%02d", 1:11))) %>%  # Only original 2D models
  mutate(median = median * 100)

pl2 <- ggplot(data = mu_pred_2d) +
    geom_tile(aes(x = n_ha, y = ba_ha, fill = median), alpha = 0.7) +
    geom_contour(aes(x = n_ha, y = ba_ha, z = median), breaks = c(10, 20, 30, 40, 50), col = 'black', lty = 'dashed') +
    geom_point(data = df, aes(x = n_ha, y = ba_ha), col = 'black', alpha = 0.7) +
    # scale_fill_gradient(low = "white", high = "black", name = "couvert") +
    scale_fill_viridis_c(direction = -1, option = 'viridis', name = 'cover (%)') +
    facet_wrap(.~ model) +
    scale_x_continuous(breaks = seq(0, 150, by = 20)) +
    scale_y_continuous(breaks = seq(0,  30, by = 5)) +
    theme_bw()
pl2
ggsave(file = here('./output/multiple_cover_diag_2d.jpg'), plot = pl2, width = 10, height = 10)

# plot cover vs dg for dg-only models (m12-m14)
mu_pred_dg <- mu_pred %>%
  filter(model %in% paste0("m", sprintf("%02d", 12:14))) %>%
  mutate(median = median * 100)

if(nrow(mu_pred_dg) > 0) {
  pl2_dg <- ggplot(data = mu_pred_dg) +
      geom_line(aes(x = dg, y = median, color = model), size = 1.2) +
      geom_point(data = df, aes(x = dg, y = cover_ellipse * 100), alpha = 0.5) +
      scale_color_viridis_d(option = 'viridis') +
      xlab('Mean diameter (dg, cm)') +
      ylab('Cover (%)') +
      theme_bw() +
      theme(legend.position = "bottom")
  pl2_dg
  ggsave(file = here('./output/cover_vs_dg.jpg'), plot = pl2_dg, width = 8, height = 6)
}

# plot cover diagrams for 3D models at different dg values
mu_pred_3d <- mu_pred %>%
  filter(model %in% paste0("m", sprintf("%02d", 15:24))) %>%
  mutate(median = median * 100)

if(nrow(mu_pred_3d) > 0) {
  # Select a few representative dg values for visualization
  dg_values <- c(25, 35, 45, 55)  # Small, medium, large, very large trees

  mu_pred_3d_subset <- mu_pred_3d %>%
    filter(dg %in% dg_values) %>%
    mutate(dg_label = paste("dg =", dg, "cm"))

  pl2_3d <- ggplot(data = mu_pred_3d_subset) +
      geom_tile(aes(x = n_ha, y = ba_ha, fill = median), alpha = 0.7) +
      geom_contour(aes(x = n_ha, y = ba_ha, z = median), breaks = c(10, 20, 30, 40, 50), col = 'black', lty = 'dashed') +
      scale_fill_viridis_c(direction = -1, option = 'viridis', name = 'cover (%)') +
      facet_grid(dg_label ~ model) +
      xlab(expression("density" ~ (n%.%ha^-1))) +
      ylab(expression("basal area" ~ (m^2%.%ha^-1))) +
      theme_bw() +
      theme(strip.text = element_text(size = 8))
  pl2_3d
  ggsave(file = here('./output/multiple_cover_diag_3d.jpg'), plot = pl2_3d, width = 12, height = 10)
}

# plot cover diagram for best model
best <- mu_pred %>% filter(model == 'm10')
# create mask based on a the basal area of n_ha trees of 5 and 11 cm dbh
# best <- best %>%
#     mutate(min_ba = n_ha * pi * (0.05^2)/4,
#             max_ba = n_ha * pi * (0.11^2)/4,
#             median = case_when(median < min_ba ~ NA,
#                                 median > max_ba ~NA,
#                                 TRUE ~ median))
# create mask base on cover value
# best <- best %>%
#     mutate(median = ifelse(median > 0.60, NA, median))

# # risome data
# risome <- read.csv2('./data/risome/Infos_producteurs_diag.csv')

# plot
pl3 <- ggplot(data = best %>% mutate(median = median * 100)) +
    geom_tile(aes(x = n_ha, y = ba_ha, fill = median), alpha = 0.7) +
    geom_contour(aes(x = n_ha, y = ba_ha, z = median), breaks = c(10, 20, 30, 40, 50), col = 'black', lty = 'dashed') +
    geom_point(data = df, aes(x = n_ha, y = ba_ha), col = 'black', alpha = 0.7) +
    # geom_point(data = risome, aes(x = densité_gros_bois, y = surface_terriere.ha), col = 'black', alpha = 0.7) +
    # scale_fill_gradient(low = "white", high = "black", name = "couvert") +
    scale_fill_viridis_c(direction = -1, option = 'viridis', name = 'cover (%)') +
    # add percentages
    annotate('text', x = 20, y = 9, label = "10%", size = 5) +
    annotate('text', x = 37, y = 11, label = "20%", size = 5) +
    annotate('text', x = 54, y = 14, label = "30%", size = 5) +
    annotate('text', x = 75, y = 17, label = "40%", size = 5) +
    annotate('text', x = 100, y = 21, label = "50%", size = 5) +
    # add management symbol
    annotate('point', x = 127, y = 25, size = 3, color = "white") +
    geom_segment(
        data = data.frame(
            x = c(127, 127, 127),
            y = c(25, 25, 25),
            xend = c(139, 127, 117),
            yend = c(25, 27.5, 23.5)
        ),
        aes(x = x, y = y, xend = xend, yend = yend),
        arrow = arrow(length = unit(0.3, "cm")),
        col = "white"
    ) +
    annotate('text', x = 149, y = 25, label = 'recruitment', col = "white") +
    annotate('text', x = 127, y = 28.2, label = 'growth', col = "white") +
    annotate('text', x = 116, y = 22.7, label = 'logging', col = "white") +
    # labels
    xlab(expression("density" ~ (n%.%ha^-1))) +
    ylab(expression("basal area" ~ (m^2%.%ha^-1))) +
    scale_x_continuous(breaks = seq(0, 150, by = 10)) +
    scale_y_continuous(breaks = seq(0,  30, by = 2)) +
    theme_bw()
pl3
ggsave(file = here('./output/cover_diag.jpg'), plot = pl3, width = 8, height = 8)

###############################################################
# cover table
###############################################################

# plot cover diagram for best model
best <- mu_pred %>% filter(model == 'm10')

# create density and mean dbh classes
pred_cl <- best %>%
  mutate(n_class = (cut(n_ha, breaks = seq(0, max(n_ha, na.rm = TRUE) + 10, by = 10), include.lowest = TRUE, right = FALSE)),
        ba_class = (cut(ba_ha, breaks = seq(0, max(ba_ha, na.rm = TRUE) + 1, by = 2), include.lowest = TRUE, right = FALSE)))

# filter n_ha < 110
pred_cl <- pred_cl %>%
    filter(n_ha < 110)

# get mean cover for each class of n and ba
mean_cover_df <- pred_cl %>%
  group_by(ba_class, n_class) %>%
  summarise(mean_cover = round(mean(median, na.rm = TRUE)*100)) %>%
  ungroup()

# pivot wider and save as semicolon separated csv
df <- mean_cover_df %>%
  pivot_wider(names_from = n_class, values_from = mean_cover)

# save
write.csv2(df, './output/cover_tab.csv', row.names = FALSE)

###############################################################
# table of max nb of recruted trees 
###############################################################

# plot cover diagram for best model
best <- mu_pred %>% filter(model == 'm10')

# get n_ha for a cover of 0.3 for each value of ba_ha
dd <- best %>%
    select(-model, -lower_ci, -upper_ci) %>%
    filter(round(median,2) == 0.30) %>%
    arrange(ba_ha, n_ha) %>%
    group_by(ba_ha) %>%
    mutate(min_dens = min(n_ha)) %>%
    filter(n_ha == min_dens)

# create classes of ba_ha
dd <- dd %>%
    mutate(ba_ha_class = (cut(ba_ha, breaks = seq(0, max(ba_ha, na.rm = TRUE) + 1, by = 2), right = FALSE))) %>%
    filter(!is.na(ba_ha_class)) %>%
    mutate(ba_ha_class = as.character(ba_ha_class),
            ba_ha_class = ifelse(ba_ha_class == '[28,30)', '[28,30]', ba_ha_class),)

# get mean n_ha for each mean ba class
dd <- dd %>%
    group_by(ba_ha_class) %>%
    summarise(mean_dens = round(mean(n_ha)))

# pivot longer df
backup <- colnames(df)
colnames(df) <- c('ba_ha_class', seq(5, 205, by = 10))
max_recru <- df %>%
    pivot_longer(cols = -ba_ha_class, names_to = 'dens', values_to = 'cover') %>%
    select(-cover) %>%
    full_join(dd, by = 'ba_ha_class')

# calculate max number of recrutment
max_recru <- max_recru %>%
    mutate(mean_dens = ifelse(mean_dens > 100, 100, mean_dens),
            dens = as.numeric(dens),
            max_recru = mean_dens - dens,
            max_recru = ifelse(max_recru < 0, 0, max_recru),
            # when ba_ha < 2 calculate max nb recruted based on current density 
            max_recru = ifelse(is.na(max_recru), 100-dens, max_recru),
            max_recru = ifelse(max_recru < 0, 0, max_recru)) %>%
            select(-mean_dens)

# pivot wider
max_recru <- max_recru %>%
    pivot_wider(names_from = dens, values_from = max_recru)
colnames(max_recru) <- backup

# save
write.csv2(max_recru, './output/max_recru_tab.csv', row.names = FALSE)
