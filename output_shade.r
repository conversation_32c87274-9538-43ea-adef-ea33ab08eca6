###############################################################
# initialisation
###############################################################

# clean up environment
rm(list = ls())

# Load packages
library(dplyr)
library(rstan)
library(ggplot2)
library(stringr)
library(viridis)
library(tidyr)
library(here)

# load calibration data
df <- readRDS(here("temp", "calib_data.RDS"))

# load model
mod <- readRDS(here("temp", "model.rds"))

# load function to extract chain quantiles
source(here("R", "extract_quantiles.r"))

###############################################################
# get parameters values
###############################################################

# get list of parameters
all_param <- mod@model_pars

# get values of parameters
# s <- summary(mod, pars = c(all_param))$summary

# get all values of the parameters from the 4 chains
params <- rstan::extract(mod)

###############################################################
# plot C4F canopy cover values
###############################################################

# plot
pl1 <- ggplot() +
    geom_point(data = df, aes(x = n_ha, y = ba_ha, col = cover_circle)) +
    scale_color_viridis_c(direction = -1, option = 'viridis') +
    xlab(expression("density" ~ (n.ha^-1))) +
    ylab("basal area (m2)") +
    theme_bw() +
    theme(
        axis.title.x = element_text(size = 20),
        axis.title.y = element_text(size = 20),
        axis.text = element_text(size = 14)
    )
pl1
ggsave(file = here('./output/c4f_calib_data.jpg'), plot = pl1, width = 5, height = 5)

###############################################################
# plot obs vs predicted
###############################################################

# extract prediction quantiles
pred1 <- extract_quantiles1(params, pred = 'pred1')

# merge with df
df <- bind_cols(df, pred1)

# plot obs vs pred
pl2 <- ggplot(data = df) +
    geom_point(aes(x = cover_circle, y = q2)) +
    # geom_point(aes(x = cover, y = q1), col = 'red') +
    # geom_point(aes(x = cover, y = q3), col = 'blue') +
    geom_abline(slope = 1, intercept = 0, linetype = "dashed", color = "blue") +
    xlim(0, 0.9) +
    ylim(0, 0.9) +
    theme_bw()
pl2
ggsave(file = here('./output/obs_vs_pred.jpg'), plot = pl2, width = 5, height = 5)

###############################################################
# get goodness of fit parameters
###############################################################

# extract r2
R2 <- extract_quantiles_rmse(params, pred = 'R2', df) %>%
    select(value) %>% rename(R2 = value)
quantile(R2$R2)[3]

# extract rmse
rmse <- extract_quantiles_rmse(params, pred = 'rmse', df) %>%
    select(value) %>% rename(rmse = value)
quantile(rmse$rmse)[3]

# extract msd
msd <- extract_quantiles_rmse(params, pred = 'msd', df) %>%
    select(value) %>% rename(msd = value)
quantile(msd$msd)[3]

# extract sb
sb <- extract_quantiles_rmse(params, pred = 'sb', df) %>%
    select(value) %>% rename(sb = value)
quantile(sb$sb)[3]

# extract nu
nu <- extract_quantiles_rmse(params, pred = 'nu', df) %>%
    select(value) %>% rename(nu = value)
quantile(nu$nu)[3]

# extract lc
lc <- extract_quantiles_rmse(params, pred = 'lc', df) %>%
    select(value) %>% rename(lc = value)
quantile(lc$lc)[3]

# msd - (lc + nu + sb) == 0
quantile(msd$msd)[3] - (quantile(lc$lc)[3] + quantile(nu$nu)[3] + quantile(sb$sb)[3])



###############################################################
###############################################################
###############################################################
###############################################################



###############################################################
# plot goodness of fit parameters
###############################################################

# bind all goodness of fit parameters together
goodness <- bind_cols(R2, rmse, msd, sb, nu, lc)

# wide to long
goodness <- goodness %>%
    pivot_longer(cols = everything(), names_to = 'param', values_to = 'value')

# plot r2 and rmse
ggplot(data = goodness %>% filter(param %in% c('R2', 'rmse', 'msd'))) +
    geom_boxplot(aes(y = value)) +
    facet_wrap(~param, scales = 'free_y') +
    theme_bw()

# calculate quantiles for lc, nu, and sb
good_quant <- goodness %>%
    filter(param %in% c('lc', 'nu', 'sb')) %>%
    group_by(param) %>%
    summarise(q50 = quantile(value, probs = 0.5)) %>%
    mutate(param = factor(param, levels = c('sb', 'nu', 'lc')))

# plot msd components
ggplot(data = good_quant, aes(x="MSD components", y=q50, fill=param)) + 
  geom_bar(stat = "identity", position = 'stack') +
  labs(x = "", y = "Value", fill = "Component") +
  scale_fill_brewer(palette = "Blues", direction = -1) +
  theme_bw() +
  theme(
    axis.title.y = element_text(size = 14),
    axis.text = element_text(size = 12),
    legend.position = "right"
  )







###############################################################
# get predictions
###############################################################

# extract prediction quantiles
pred <- extract_quantiles(params, pred = 'pred')
pred <- pred %>%
    # filter(mean_dbh>=10) %>%
    rename(cover = q2)


#TODO: exclude top and bottom based on assumptions trees >cm dbh or all == 10cm dbh

# plot
pl2 <- ggplot() +
    geom_tile(data = pred, aes(x = dens, y = ba_ha, fill = cover)) +
    geom_contour(data = pred, aes(x = dens, y = ba_ha, z = cover), breaks = c(0.1, 0.2, 0.3, 0.4, 0.5), col = 'black', lty = 'dashed') +
    geom_point(data = df, aes(x = n_ha, y = ba_ha), col = 'black', alpha = 0.7) +
    # geom_contour(data = pred, aes(x = dens, y = ba_ha, z = cover), breaks = c(0.3), col = 'white') +
    # scale_fill_gradient2(low = "red", mid = "green", high = "black", midpoint = 0.25) +
    scale_fill_gradient(low = "white", high = "black", name = "couvert") +
    geom_text(aes(x = 25, y = 20, label = "10%"), size = 5) +
    geom_text(aes(x = 50, y = 23, label = "20%"), size = 5) +
    geom_text(aes(x = 70, y = 28, label = "30%"), size = 5) +
    geom_text(aes(x = 80, y = 37, label = "40%"), size = 5) +
    geom_text(aes(x = 93, y = 46, label = "50%"), size = 5) +
    geom_point(aes(x = 140, y = 60), size = 3, col = "white") +
    geom_segment(
        aes(x = c(140, 140, 140), y = c(60, 60, 60), xend = c(165, 140, 130), yend = c(60, 70, 55)),  # Start and end points of the arrow
        arrow = arrow(length = unit(0.3, "cm")), col = "white") +
    annotate('text', x = 185, y = 60, label = 'recrutement', col = "white") +
    annotate('text', x = 140, y = 73, label = 'croissance', col = "white") +
    annotate('text', x = 130, y = 53, label = 'exploitation', col = "white") +
    # scale_fill_viridis_c(direction = -1, option = 'viridis') +
    xlab(expression("densité" ~ (n.ha^-1))) +
    ylab("diametre moyen (cm)") +
    # annotate('text', x = 150, y = 60, label = paste0('rmse = ', rmse, '%'), col = 'orange') +
    # annotate('text', x = 150, y = 65, label = paste0('r2 = ', r2), col = 'orange') +
    # ylim(0,40) +
    theme_bw() +
    theme(
        axis.title.x = element_text(size = 20),
        axis.title.y = element_text(size = 20),
        axis.text = element_text(size = 14)
    )
pl2
ggsave(file = './output/shade_pred4.jpg', plot = pl2, width = 8, height = 8)

# simplified plot
pl3 <- ggplot() +
    geom_contour(data = pred, aes(x = dens, y = mean_dbh, z = cover), breaks = c(0.1, 0.2, 0.4, 0.5), col = 'black', lty = 'dashed') +
    geom_contour(data = pred, aes(x = dens, y = mean_dbh, z = cover), breaks = c(0.3), col = 'red') +
    geom_point(data = rna, aes(x = n_ha, y = mean_dbh), col = 'black', alpha = 0.7) +
    geom_text(aes(x = 25, y = 20, label = "10%"), size = 5) +
    geom_text(aes(x = 50, y = 23, label = "20%"), size = 5) +
    geom_text(aes(x = 70, y = 28, label = "30%"), size = 5, col = "red") +
    geom_text(aes(x = 80, y = 37, label = "40%"), size = 5) +
    geom_text(aes(x = 93, y = 46, label = "50%"), size = 5) +
    xlab(expression("density" ~ (n.ha^-1))) +
    ylab("mean diameter (cm)") +
    geom_point(aes(x = 140, y = 60), size = 3) +
    geom_segment(
        aes(x = c(140, 140, 140), y = c(60, 60, 60), xend = c(165, 140, 130), yend = c(60, 70, 55)),  # Start and end points of the arrow
        arrow = arrow(length = unit(0.3, "cm"))) +
    annotate('text', x = 187, y = 60, label = 'recruitment') +
    annotate('text', x = 140, y = 73, label = 'growth') +
    annotate('text', x = 130, y = 53, label = 'harvesting') +
    theme_bw() +
        theme(
        axis.title.x = element_text(size = 20),
        axis.title.y = element_text(size = 20),
        axis.text = element_text(size = 14)
    ) +
    # geom_vline(xintercept = 70, color = "orange", linetype = "dashed", size = 0.5)
    geom_rect(aes(xmin = 0, xmax = 25, ymin = -Inf, ymax = Inf), fill = "grey", alpha = 0.2) +
    geom_rect(aes(xmin = 100, xmax = 200, ymin = -Inf, ymax = Inf), fill = "grey", alpha = 0.2)
    # xlim(0, 150)
    # geom_ribbon(data = pred %>% filter(round(cover, 2) == 0.3), aes(x = dens, ymin = mean_dbh, ymax = Inf), fill = "grey", alpha = 0.5)

pl3
ggsave(file = './output/exam.jpg', plot = pl3, width = 5, height = 5)


# cover = f(density)
ggplot() +
    geom_point(data = df, aes(x = n_ha, y = cover/100)) +
    geom_line(data = pred %>% filter(mean_dbh == 15), aes(x = dens, y = cover), col = 'red') +
    geom_line(data = pred %>% filter(mean_dbh == 20), aes(x = dens, y = cover), col = 'green') +
    geom_line(data = pred %>% filter(mean_dbh == 30), aes(x = dens, y = cover), col = 'blue') +
    geom_line(data = pred %>% filter(mean_dbh == 40), aes(x = dens, y = cover), col = 'orange') +
    geom_line(data = pred %>% filter(mean_dbh == 50), aes(x = dens, y = cover), col = 'black') +
    geom_line(data = pred %>% filter(mean_dbh == 60), aes(x = dens, y = cover), col = 'grey') +
    theme_bw()

# cover = f(mean_dbh)
ggplot() +
    geom_point(data = df, aes(x = mean_dbh, y = cover/100)) +
    geom_line(data = pred %>% filter(dens == 25), aes(x = mean_dbh, y = cover), col = 'red') +
    geom_line(data = pred %>% filter(dens == 50), aes(x = mean_dbh, y = cover), col = 'green') +
    geom_line(data = pred %>% filter(dens == 75), aes(x = mean_dbh, y = cover), col = 'blue') +
    theme_bw()


###############################################################
# create a table to get shade proportion from dens and mean dbh
###############################################################

# create density and mean dbh classes
pred_cl <- pred %>%
  mutate(dens_class = (cut(dens, breaks = seq(0, max(dens, na.rm = TRUE) + 10, by = 10), include.lowest = TRUE, right = FALSE)),
        mean_dbh_class = (cut(mean_dbh, breaks = seq(0, max(mean_dbh, na.rm = TRUE) + 5, by = 5), include.lowest = TRUE, right = FALSE)))

# get mean cover for each class of density and mean_dbh
mean_cover_df <- pred_cl %>%
  group_by(mean_dbh_class, dens_class) %>%
  summarise(mean_cover = round(mean(cover, na.rm = TRUE)*100)) %>%
  ungroup()

# pivot wider
df <- mean_cover_df %>%
  pivot_wider(names_from = dens_class, values_from = mean_cover)

# save
write.csv(df, './output/tab.csv')


###############################################################
# create a table to get max nb of trees to add for a specific
# set of dens and mean dbh
###############################################################

# get tree density for a cover of 0.3 for each value of mean dbh
dd <- pred %>%
    ungroup() %>%
    select(-q1, -q3) %>%
    mutate(cover = round(cover, 2)) %>%
    filter(cover == 0.3) %>%
    arrange(mean_dbh, dens) %>%
    group_by(mean_dbh) %>%
    mutate(min_dens = min(dens)) %>%
    filter(dens == min_dens)

# create classes of mean dbh
dd <- dd %>%
    mutate(mean_dbh_class = (cut(mean_dbh, breaks = seq(0, max(mean_dbh, na.rm = TRUE) + 5, by = 5), include.lowest = TRUE, right = FALSE)))
# change "]" into ")" in mean_dbh_class
dd$mean_dbh_class <- gsub(']', ')', dd$mean_dbh_class)
# change '[80,85) into '[80,85]' in mean_dbh_class
dd <- dd %>% mutate(mean_dbh_class = ifelse(mean_dbh_class == '[80,85)', '[80,85]', mean_dbh_class))


# get mean density for each mean dbh class
dd <- dd %>%
    group_by(mean_dbh_class) %>%
    summarise(mean_dens = round(mean(dens)))

# pivot longer df
backup <- colnames(df)
colnames(df) <- c('mean_dbh_class', seq(5, 205, by = 10))
max_recru <- df %>%
    pivot_longer(cols = -mean_dbh_class, names_to = 'dens', values_to = 'cover') %>%
    select(-cover) %>%
    full_join(dd, by = 'mean_dbh_class')

# calculate max number of recrutment
max_recru <- max_recru %>%
    mutate(mean_dens = ifelse(mean_dens > 100, 100, mean_dens),
            dens = as.numeric(dens),
            max_recru = mean_dens - dens,
            max_recru = ifelse(max_recru < 0, 0, max_recru)) %>%
            select(-mean_dens)

# pivot wider
max_recru <- max_recru %>%
    pivot_wider(names_from = dens, values_from = max_recru)
colnames(max_recru) <- backup

# save
write.csv(max_recru, './output/max_recru.csv')



###############################################################
# add harvesting loss
###############################################################

# load simulations
loss <- readRDS('./temp/sim_loss.rds') %>%
    mutate(n_tree_cut = as.factor(n_tree_cut)) %>%
    filter(n_tree_cut %in% c(1, 5, 10))

# plot
pl4 <- ggplot() +
    geom_point(data = loss, aes(x = nha, y = m_dbh), col = 'black', alpha = 0.7) +
    geom_segment(data = loss,
        aes(x = nha, y = m_dbh, xend = nha - as.numeric(as.character(n_tree_cut)), yend = q2, col = n_tree_cut),  # Start and end points of the arrow
        arrow = arrow(length = unit(0.1, "cm"))) +
    # scale_color_viridis_d(option = "inferno", direction = 1) +
    scale_color_manual(values = c("lightgrey", "grey", "black")) +
    geom_contour(data = pred, aes(x = dens, y = mean_dbh, z = cover), breaks = c(0.1, 0.2, 0.4), col = 'black', lty = 'dashed') +
    geom_contour(data = pred, aes(x = dens, y = mean_dbh, z = cover), breaks = c(0.3), col = 'red') +
    geom_text(aes(x = 25, y = 20, label = "10%"), size = 5) +
    geom_text(aes(x = 50, y = 23, label = "20%"), size = 5) +
    geom_text(aes(x = 70, y = 28, label = "30%"), size = 5) +
    geom_text(aes(x = 80, y = 37, label = "40%"), size = 5) +
    xlab(expression("density" ~ (n.ha^-1))) +
    ylab("mean diameter (cm)") +
    labs(color = "number of trees harvested") +
    theme_bw() +
    theme(
        axis.title.x = element_text(size = 20),
        axis.title.y = element_text(size = 20),
        axis.text = element_text(size = 14),
        legend.position = 'bottom'
    )
pl4
ggsave(file = './output/harvest.jpg', plot = pl4, width = 5, height = 5)
