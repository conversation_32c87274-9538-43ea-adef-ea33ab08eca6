###############################################################
# Test script for dg model outputs
###############################################################

# clean up environment
rm(list = ls())

# Load packages
library(dplyr)
library(tidyr)
library(rstan)
library(ggplot2)
library(here)

# load calibration data
df <- readRDS(here("temp", "multiple_calib_data.RDS"))

# load test prediction data grids
pred_grids <- readRDS(here("temp", "test_pred_data.rds"))

# load test models
mod <- readRDS(here("temp", "test_dg_models.rds"))

# load function to extract chain quantiles
source(here("R", "extract_quantiles_multiple_models.r"))

###############################################################
# goodness of fit
###############################################################

# extract goodness of fit index quantiles
good <- extract_good(mod)

# plot R2 ranking
r2 <- ggplot(data = good %>%
         filter(parameter == 'R2') %>%
         arrange(median)) +
    geom_bar(aes(x = reorder(model, median, decreasing = TRUE), y = median), stat = "identity", fill = 'skyblue') +
    geom_errorbar(aes(x = model, ymin = lower_ci, ymax = upper_ci), width=0.4, colour="orange", alpha=0.9) +
    xlab('model') +
    ylab(expression(R^2)) +
    theme_bw()
r2
ggsave(file = here('./output/test_r2.jpg'), plot = r2, width = 8, height = 6)

###############################################################
# plot obs vs predicted
###############################################################

# extract pred quantiles
pred <- extract_pred(mod)

# merge with df
# (df must first be duplicated to match the number of rows in pred = for each model)
# 1) stretch df to match number of rows in pred
df_rep <- df %>% 
  slice(rep(row_number(), length.out = nrow(pred)))
# 2) bind them side–by–side
df_rep <- bind_cols(df_rep, pred)

# plot obs vs pred with R2 values
pl1 <- ggplot(data = df_rep) +
    geom_point(aes(x = cover_ellipse, y = median), alpha = 0.5) +
    geom_abline(slope = 1, intercept = 0, linetype = "dashed", color = "red") +
    facet_wrap(.~ model) +
    coord_fixed(ratio = 1) +
    theme_bw()
pl1
ggsave(file = here('./output/test_obs_vs_pred.jpg'), plot = pl1, width = 10, height = 8)

###############################################################
# cover diagrams
###############################################################

# extract mu_pred quantiles
mu_pred <- extract_mu_pred(mod, pred_grids)

# plot cover diagram for 2D models only (models with n_ha and ba_ha)
mu_pred_2d <- mu_pred %>% 
  filter(model %in% c("m01", "m10")) %>%  # Only 2D models from our test
  mutate(median = median * 100)

if(nrow(mu_pred_2d) > 0) {
  pl2 <- ggplot(data = mu_pred_2d) +
      geom_tile(aes(x = n_ha, y = ba_ha, fill = median), alpha = 0.7) +
      geom_contour(aes(x = n_ha, y = ba_ha, z = median), breaks = c(10, 20, 30, 40, 50), col = 'black', lty = 'dashed') +
      geom_point(data = df, aes(x = n_ha, y = ba_ha), col = 'black', alpha = 0.7) +
      scale_fill_viridis_c(direction = -1, option = 'viridis', name = 'cover (%)') +
      facet_wrap(.~ model) +
      theme_bw()
  pl2
  ggsave(file = here('./output/test_cover_diag_2d.jpg'), plot = pl2, width = 10, height = 6)
}

# plot cover vs dg for dg-only models (m12-m13)
mu_pred_dg <- mu_pred %>% 
  filter(model %in% c("m12", "m13")) %>%
  mutate(median = median * 100)

if(nrow(mu_pred_dg) > 0) {
  pl2_dg <- ggplot(data = mu_pred_dg) +
      geom_line(aes(x = dg, y = median, color = model), size = 1.2) +
      geom_point(data = df, aes(x = dg, y = cover_ellipse * 100), alpha = 0.5) +
      scale_color_viridis_d(option = 'viridis') +
      xlab('Mean diameter (dg, cm)') +
      ylab('Cover (%)') +
      theme_bw() +
      theme(legend.position = "bottom")
  pl2_dg
  ggsave(file = here('./output/test_cover_vs_dg.jpg'), plot = pl2_dg, width = 8, height = 6)
}

# plot cover diagrams for 3D models at different dg values
mu_pred_3d <- mu_pred %>% 
  filter(model %in% c("m17", "m24")) %>%
  mutate(median = median * 100)

if(nrow(mu_pred_3d) > 0) {
  # Select a few representative dg values for visualization
  dg_values <- c(20, 40, 60, 80)  # From our test grid
  
  mu_pred_3d_subset <- mu_pred_3d %>%
    filter(dg %in% dg_values) %>%
    mutate(dg_label = paste("dg =", dg, "cm"))
  
  if(nrow(mu_pred_3d_subset) > 0) {
    pl2_3d <- ggplot(data = mu_pred_3d_subset) +
        geom_tile(aes(x = n_ha, y = ba_ha, fill = median), alpha = 0.7) +
        geom_contour(aes(x = n_ha, y = ba_ha, z = median), breaks = c(10, 20, 30, 40, 50), col = 'black', lty = 'dashed') +
        scale_fill_viridis_c(direction = -1, option = 'viridis', name = 'cover (%)') +
        facet_grid(dg_label ~ model) +
        xlab(expression("density" ~ (n%.%ha^-1))) +
        ylab(expression("basal area" ~ (m^2%.%ha^-1))) +
        theme_bw() +
        theme(strip.text = element_text(size = 8))
    pl2_3d
    ggsave(file = here('./output/test_cover_diag_3d.jpg'), plot = pl2_3d, width = 10, height = 8)
  }
}

cat("Test output completed successfully!\n")

# Print summary of results
cat("\nModel performance summary (R²):\n")
r2_summary <- good %>% 
  filter(parameter == 'R2') %>%
  arrange(desc(median)) %>%
  select(model, median, lower_ci, upper_ci)
print(r2_summary)
