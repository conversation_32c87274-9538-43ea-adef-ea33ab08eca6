# function to extract prediction quantiles
extract_quantiles <- function(params, pred = 'pred'){
  result <- data.frame()
  for (i in 1:200){
    c1 <- as.data.frame(t(as.data.frame(params[[pred]][,i,])))
    c1 <- as.data.frame(t(apply(c1, 1, quantile, probs = c(0.025, 0.5, 0.975))))
    names(c1) <- c('q1', 'q2', 'q3')
    c1$dens <- i
    c1$ba_ha <- c(1:30)
    result <- rbind(result, c1)
  }
  return(result)
}

# function to extract prediction quantiles for pred vs obs
extract_quantiles1 <- function(params, pred = 'pred1'){
  c1 <- as.data.frame(t(as.data.frame(params[[pred]])))
  c1 <- as.data.frame(t(apply(c1, 1, quantile, probs = c(0.025, 0.5, 0.975))))
  names(c1) <- c('q1', 'q2', 'q3')
  return(c1)
}

# function to extract rmse
extract_quantiles_rmse <- function(params, pred = 'rmse', df){

  c1 <- as.data.frame(params[[pred]])
  names(c1) <- 'value'
  c1$mod <- pred

  return(c1)

}

# Function to extract prediction quantiles
extract_array <- function(params, pred = 'pred_mu_loss') {
  
  # Extract the array of predictions
  a <- params[[pred]]
  
  # Get dimensions
  iterations <- dim(a)[1]
  N_nha <- dim(a)[2]
  N_mdbh <- dim(a)[3]
  N_n_tree_cut <- dim(a)[4]
  
  # Define the predictor values (ensure they match the Stan model)
  nha_vals <- c(25, 50, 75, 100, 125, 150, 175, 200)
  m_dbh_vals <- c(20, 30, 40, 50, 60, 70, 80)
  n_tree_cut_vals <- c(1, 2, 5, 10)
  
  # Create an empty data frame
  result <- data.frame()
  
  # Loop over all combinations of predictors
  for (i in 1:N_nha) {
    for (j in 1:N_mdbh) {
      for (k in 1:N_n_tree_cut) {
        # Extract samples for the current combination
        samples <- a[, i, j, k]
        
        # Compute quantiles
        quantiles <- quantile(samples, probs = c(0.025, 0.5, 0.975))
        
        # Create a data frame row with quantiles and predictor values
        df_row <- data.frame(
          q1 = quantiles[1],
          q2 = quantiles[2],
          q3 = quantiles[3],
          nha = nha_vals[i],
          m_dbh = m_dbh_vals[j],
          n_tree_cut = n_tree_cut_vals[k]
        )
        
        # Append the row to the result data frame
        result <- rbind(result, df_row)
      }
    }
  }
  
  # Return the result data frame
  return(result)
}