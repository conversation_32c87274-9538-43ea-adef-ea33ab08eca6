# Define a function to compute both circle-based and ellipse-based tree cover per cluster/type
tree_cover <- function(tree, map) {
  # 0. Compute plot area for each polygon in 'map'
  map$pl_surf <- expanse(map)             # expanse() gives area of each plot
  plot_surf <- as.data.frame(map) %>%     # convert to data.frame for easy joins
    select(clus, type, pl_surf)
  
  # ── A. CIRCLE-BASED CROWN CALCULATION ──
  # 1. Extract tree locations and mean radius
  pts_circle <- tree %>%
    select(clus, type, stemID, long, lat, mr) %>%
    vect(geom = c("long","lat"), crs = crs(map))   # make SpatVector points in same CRS as map
  
  # 2. Create circular buffers around each tree with radius = mr
  buf_circle <- buffer(pts_circle, width = "mr")
  
  # 3. Dissolve buffers by cluster and type to get total canopy surface
  crown_circle <- aggregate(buf_circle, by = c("clus","type"))
  
  # 4. Compute total circle-based crown area per cluster/type
  crown_circle$ca_circle <- expanse(crown_circle)
  
  # 5. Convert the result back to a data.frame
  circle_surf <- as.data.frame(crown_circle) %>%
    select(clus, type, ca_circle)
  
  # ── B. ELLIPSE-BASED CROWN CALCULATION ──
  df <- tree
  
  # 1. Build centre points in geographic coords
  coords_ll <- as.matrix(df[, c("long","lat")])    # matrix of lon/lat
  pts_ll    <- vect(coords_ll, type="points", crs="EPSG:4326")
  
  # 2. Choose an appropriate UTM zone (units in metres)
  mean_lon <- mean(df$long)                        # average longitude of all trees
  zone     <- floor((mean_lon + 180)/6) + 1        # compute UTM zone number
  utm_crs  <- sprintf("EPSG:%d", 32600 + zone)     # build UTM EPSG code
  
  # 3. Project centre points into UTM CRS for metre operations
  pts_utm  <- project(pts_ll, utm_crs)
  coords_m <- crds(pts_utm)                       # extract x,y in metres for each tree
  
  # 4. Precompute bearing angles for 8 directions (math convention)
  bearings_deg <- c(0,45,90,135,180,225,270,315)    # E,NE,N,NW,W,SW,S,SE
  br_rad       <- bearings_deg * pi/180            # convert degrees to radians
  
  # 5. Prepare interpolation grid for smooth ellipse boundary
  M       <- 360                                   # number of points to sample
  ang_rad <- seq(0,2*pi,length.out=M)              # full circle in radians
  br_wrap <- c(br_rad, 2*pi)                       # wrap-around for interpolation
  
  # 6. Build one ellipse polygon per tree in UTM coordinates
  ell_list <- vector("list", nrow(df))
  for(i in seq_len(nrow(df))) {
    # extract the eight directional radii (metres)
    rads_all <- as.numeric(df[i, c("cE","cNE","cN","cNO",
                                   "cO","cSO","cS","cSE")])
    valid    <- !is.na(rads_all)                   # identify non-NA radii
    
    if(sum(valid) < 2) {
      # if fewer than 2 valid directions, fallback to circular buffer
      radius <- df$mr[i]
      ell_list[[i]] <- buffer(
        vect(matrix(coords_m[i,], ncol=2), crs = utm_crs),
        width = radius
      )
      next
    }
    
    # keep only valid bearings and radii
    rv      <- rads_all[valid]
    bv      <- br_rad[valid]
    wrap_b  <- c(bv, bv[1] + 2*pi)                 # wrap for continuous interp
    wrap_r  <- c(rv, rv[1])
    
    # interpolate radii to smooth boundary
    r_smooth <- approx(wrap_b, wrap_r, xout = ang_rad, rule = 2)$y
    
    # convert polar to Cartesian coordinates in metres
    x0 <- coords_m[i,1];  y0 <- coords_m[i,2]
    xy <- cbind(
      x = x0 + r_smooth * cos(ang_rad),
      y = y0 + r_smooth * sin(ang_rad)
    )
    xy <- rbind(xy, xy[1,])                       # close the polygon ring
    
    # create a SpatVector polygon in UTM CRS
    ell_list[[i]] <- vect(xy, type="polygons", crs = utm_crs)
  }

  # 7. Combine all ellipse polygons and reproject back to WGS84
  ell_utm <- do.call(rbind, ell_list)             # merge list into one SpatVector
  ell_ll  <- project(ell_utm, "EPSG:4326")        # back to lon/lat
  ell_ll$clus <- df$clus                          # carry over cluster IDs
  ell_ll$type <- df$type                          # carry over type IDs

  # 8. Dissolve ellipses by cluster and type
  crown_ellipse <- aggregate(ell_ll, by = c("clus","type"))

  # 9. Compute ellipse-based crown area
  crown_ellipse$ca_ellipse <- expanse(crown_ellipse)
  ellipse_surf <- as.data.frame(crown_ellipse) %>%
    select(clus, type, ca_ellipse)

  # ── C. MERGE RESULTS AND COMPUTE COVER RATIOS ──
  out <- plot_surf %>%
    left_join(circle_surf,  by = c("clus","type")) %>%
    left_join(ellipse_surf, by = c("clus","type")) %>%
    mutate(
      ca_circle    = replace_na(ca_circle,    0),  # zero if no trees
      ca_ellipse   = replace_na(ca_ellipse,   0),
      cover_circle  = ca_circle  / pl_surf,        # fraction of plot covered
      cover_ellipse = ca_ellipse / pl_surf
    )
# D. MAP PLOT OF BOTH METHODS + TREE TRUNKS USING GGPLOT
  plot_all_forests <- function(map, crown_circle, crown_ellipse, tree) {
    # Get unique combinations of clus and type
    plot_combos <- unique(as.data.frame(map)[, c("clus", "type")])
    
    # Convert spatial objects to sf for ggplot
    map_sf <- st_as_sf(map)
    crown_circle_sf <- st_as_sf(crown_circle)
    crown_ellipse_sf <- st_as_sf(crown_ellipse)
    
    # Create list to store plots
    plot_list <- list()
    
    # Create one plot for each combination
    for(i in 1:nrow(plot_combos)) {
      current_clus <- plot_combos$clus[i]
      current_type <- plot_combos$type[i]
      
      # Filter data for current plot
      current_map <- map_sf[map_sf$clus == current_clus & map_sf$type == current_type,]
      current_circle <- crown_circle_sf[crown_circle_sf$clus == current_clus & crown_circle_sf$type == current_type,]
      current_ellipse <- crown_ellipse_sf[crown_ellipse_sf$clus == current_clus & crown_ellipse_sf$type == current_type,]
      
      # Filter trees and convert to sf for spatial operations
      current_trees_df <- tree[tree$clus == current_clus & tree$type == current_type,]
      
      # Determine the UTM CRS for the current map/trees based on mean longitude
      mean_lon_current <- mean(current_trees_df$long)
      zone_current     <- floor((mean_lon_current + 180)/6) + 1
      utm_crs_current  <- sprintf("EPSG:%d", 32600 + zone_current)
      
      # Convert tree data to sf and project to UTM for accurate diameter scaling
      current_trees_sf <- st_as_sf(current_trees_df, coords = c("long", "lat"), crs = "EPSG:4326") %>%
        st_transform(crs = utm_crs_current) # Project to UTM
      
      # Extract coordinates for geom_circle and labels
      current_trees_coords <- as.data.frame(st_coordinates(current_trees_sf))
      current_trees_sf$x0 <- current_trees_coords$X
      current_trees_sf$y0 <- current_trees_coords$Y

      # IMPORTANT: Ensure dbh is in meters for direct use as radius (diameter/2)
      # Assuming 'dbh' is in cm:
      current_trees_sf$radius <- current_trees_sf$dbh / 200 

      # --- Prepare data for 8 radii lines ---
      # Bearings (math convention: 0=East, 90=North, etc.)
      bearings_rad <- c(0, pi/4, pi/2, 3*pi/4, pi, 5*pi/4, 3*pi/2, 7*pi/4) # E, NE, N, NW, W, SW, S, SE

      radii_lines_df_list <- list()
      if(nrow(current_trees_df) > 0) { # Only proceed if there are trees
        for (j in 1:nrow(current_trees_df)) {
          tree_row <- current_trees_df[j, ]
          x_center <- current_trees_sf$x0[j]
          y_center <- current_trees_sf$y0[j]

          # Extract the 8 radii values for the current tree
          radii_values <- as.numeric(tree_row[, c("cE","cNE","cN","cNO",
                                                  "cO","cSO","cS","cSE")])

          # Create a dataframe for the 8 segments of the current tree
          tree_segments <- data.frame(
            x = x_center,
            y = y_center,
            xend = x_center + radii_values * cos(bearings_rad),
            yend = y_center + radii_values * sin(bearings_rad),
            clus = current_clus,
            type = current_type,
            stemID = tree_row$stemID # Keep stemID for potential debugging
          )
          radii_lines_df_list[[j]] <- tree_segments
        }
      }
      radii_lines_df <- do.call(rbind, radii_lines_df_list)
      # ------------------------------------

      # Create plot
      p <- ggplot() +
        # Base map - project map to the current UTM CRS for consistent plotting
        geom_sf(data = st_transform(current_map, crs = utm_crs_current), 
                fill = "lightgrey", color = "lightgrey", alpha = 0.5) +
        # Circle crowns - project to current UTM CRS
        geom_sf(data = st_transform(current_circle, crs = utm_crs_current), 
                aes(fill = "Circle Crowns"), color = "blue", alpha = 0.4, 
                key_glyph = "point") +
        # Ellipse crowns - project to current UTM CRS
        geom_sf(data = st_transform(current_ellipse, crs = utm_crs_current), 
                aes(fill = "Ellipse Crowns"), color = "lightgreen", alpha = 0.6,
                key_glyph = "point") +
        # Add the 8 radii measurements as lines
        geom_segment(data = radii_lines_df,
                     aes(x = x, y = y, xend = xend, yend = yend),
                     color = "black",
                     linetype = "dotted",
                     linewidth = 0.2,
                     alpha = 0.7) +
        # Tree trunks using geom_circle for scale
        geom_circle(data = current_trees_sf,
                    aes(x0 = x0, y0 = y0, r = radius, fill = "Tree Trunks"),
                    color = "black", alpha = 1,
                    key_glyph = "point") +
        # Title and theme
        ggtitle(sprintf("%s - %s", current_clus, current_type)) +
        theme_minimal() +
        theme(
          plot.title = element_text(hjust = 0.5),
          legend.position = "bottom",
          legend.box = "horizontal"
        ) +
        # Manually define colors and fills for the legend
        scale_fill_manual(
          name = "",
          values = c("Circle Crowns" = alpha("blue", 0.4), 
                     "Ellipse Crowns" = alpha("lightgreen", 0.6),
                     "Tree Trunks" = "black"), 
          guide = guide_legend(
            override.aes = list(
              linetype = "blank", 
              shape = 21,         # Shape 21 is a filled circle
              size = 5,           # Adjust size as needed
              color = "black",    # Outline color for shape 21
              stroke = 0.5        # Outline thickness for shape 21
            )
          )
        ) +
        guides(
          fill = guide_legend(order = 1) 
        ) +
        coord_sf() +
        xlab('longitude') +
        ylab('latitude')
      
      plot_list[[i]] <- p 
    }
    
    # Save all plots to PDF
    pdf(here("output/forest_plots.pdf"))
    for(p in plot_list) {
      print(p)
    }
    dev.off()
  }
  
  # Call the function to plot all forests
  plot_all_forests(map, crown_circle, crown_ellipse, tree)

  return(out)
}