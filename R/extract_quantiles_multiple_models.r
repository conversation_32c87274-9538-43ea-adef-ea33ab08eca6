# function to extract pred
extract_pred <- function(mod){
  all_model_pred <- data.frame()
  for (model_name in names(mod)) {
    # Extract the 'pred' generated quantities from the fitted Stan model
    pred <- rstan::extract(mod[[model_name]], pars = "pred")$pred
    # Calculate median and credible intervals
    lower_ci <- apply(pred, 2, quantile, probs = 0.025)
    median <- apply(pred, 2, quantile, probs = 0.5)  # Fixed: was 0.025, should be 0.5
    upper_ci <- apply(pred, 2, quantile, probs = 0.975)
    # Combine the prediction data grid with the calculated predictions
    model_pred <- data.frame(model = model_name,
                          median = median,
                          lower_ci = lower_ci,
                          upper_ci = upper_ci)
    all_model_pred <- rbind(all_model_pred, model_pred)
  }
  return(all_model_pred)
}

# function to extract goodness of fit metrics
extract_good <- function(mod){
  all_model_good <- data.frame()
  for (metric in c("R2", "rmse", "msd", 'sb', 'nu', 'lc')) {
    for (model_name in names(mod)) {
      # Extract the specified parameter from the fitted Stan model
      good <- rstan::extract(mod[[model_name]], pars = metric)[[metric]]
      # Calculate median and credible intervals
      lower_ci <- quantile(good, probs = 0.025)
      median <- quantile(good, probs = 0.5)
      upper_ci <- quantile(good, probs = 0.975)
      # Combine the prediction data grid with the calculated predictions
      model_good <- data.frame(model = model_name,
                            parameter = metric,
                            median = median,
                            lower_ci = lower_ci,
                            upper_ci = upper_ci)
      all_model_good <- rbind(all_model_good, model_good)
    }
  }
  return(all_model_good)
}

# function to extract mu_pred
extract_mu_pred <- function(mod, pred_grids){
  all_model_mu_pred <- data.frame()
  for (model_name in names(mod)) {
    # Extract the 'mu_pred' generated quantities from the fitted Stan model
    mu_pred <- rstan::extract(mod[[model_name]], pars = "mu_pred")$mu_pred
    # Calculate median and credible intervals
    lower_ci <- apply(mu_pred, 2, quantile, probs = 0.025)
    median <- apply(mu_pred, 2, quantile, probs = 0.5)  # Fixed: was 0.025, should be 0.5
    upper_ci <- apply(mu_pred, 2, quantile, probs = 0.975)

    # Determine which prediction grid to use based on model name
    # Models m01-m11: 2D grid (n_ha, ba_ha)
    # Models m12-m14: 1D grid (dg only)
    # Models m15+: 3D grid (n_ha, ba_ha, dg)
    if (model_name %in% paste0("m", sprintf("%02d", 1:11))) {
      current_grid <- pred_grids$grid_2d
    } else if (model_name %in% paste0("m", sprintf("%02d", 12:14))) {
      current_grid <- pred_grids$grid_dg
    } else {
      current_grid <- pred_grids$grid_3d
    }

    # Combine the prediction data grid with the calculated predictions
    model_mu_pred <- data.frame(model = model_name,
                          median = median,
                          lower_ci = lower_ci,
                          upper_ci = upper_ci)
    model_mu_pred <- bind_cols(model_mu_pred, current_grid)
    all_model_mu_pred <- rbind(all_model_mu_pred, model_mu_pred)
  }
  return(all_model_mu_pred)
}
