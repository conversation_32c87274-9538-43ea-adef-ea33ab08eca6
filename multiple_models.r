###############################################################
# initialisation
###############################################################

# clean up environment
rm(list = ls())

# load packages
library(dplyr)
library(tidyr)
library(terra)
library(rstan)
options(mc.cores = parallel::detectCores())
library(ineq)
library(shinystan)
library(here)
library(ggplot2)
library(sf)
library(ggforce)

# load function to extract chain quantiles
source(here('R/tree_cover.r'))

# load tree data
tree <- readRDS(here('./data/treeDB.RDS')) %>%
        filter(dbh >= 10) #%>% # TODO: carefull! it removes trees with no dbh and htot which might affect the final results (?)
        # filter(htot >= 5)

# load plot map
map <- vect(here('./data/sampling.shp'))

###############################################################
# calculate tree cover
###############################################################

# calculate [m]ean [r]adius
tree <- tree %>%
        mutate(mr = rowMeans(across(cN:cNO), na.rm = TRUE))

# TODO: try with new coord ?
# TODO: manage trees with no crowns (their crowns could be modeled?: check their number)
# TODO: manage trees with no coordinates...
tree <- tree %>%
        filter(!is.na(mr)) %>%
        filter(mr>0) %>%
        filter(!is.na(long), !is.na(lat))

# Function to fill missing radii with mean of adjacent directions
fill_missing_radii <- function(df) {
  df %>%
    mutate(
      cN  = if_else(is.na(cN),
                    rowMeans(across(c(cNO, cNE)), na.rm = TRUE),
                    cN),
      cE  = if_else(is.na(cE),
                    rowMeans(across(c(cNE, cSE)), na.rm = TRUE),
                    cE),
      cS  = if_else(is.na(cS),
                    rowMeans(across(c(cSE, cSO)), na.rm = TRUE),
                    cS),
      cO  = if_else(is.na(cO),
                    rowMeans(across(c(cSO, cNO)), na.rm = TRUE),
                    cO),
      cNE = if_else(is.na(cNE),
                    rowMeans(across(c(cN,  cE)), na.rm = TRUE),
                    cNE),
      cSE = if_else(is.na(cSE),
                    rowMeans(across(c(cS,  cE)), na.rm = TRUE),
                    cSE),
      cSO = if_else(is.na(cSO),
                    rowMeans(across(c(cS,  cO)), na.rm = TRUE),
                    cSO),
      cNO = if_else(is.na(cNO),
                    rowMeans(across(c(cN,  cO)), na.rm = TRUE),
                    cNO)
    )
}

# Apply the function twice to fill missing radii
tree <- fill_missing_radii(tree)
tree <- fill_missing_radii(tree)

# calculate tree cover
df <- tree_cover(tree, map)

###############################################################
# calculate dendro variables
###############################################################

dendro <- tree %>%
        mutate(ba = pi*((dbh/100)^2)/4,
                dbh2 = dbh^2) %>%
        group_by(clus, type) %>%
        summarise(n = n(),
                  BA = sum(ba),
                  mean_dbh = mean(dbh),
                  med_dbh = median(dbh),
                  dg = sqrt( sum(dbh2) / sum(n) ),
                  gini = Gini(dbh))

# merge dendro and cover data
df <- full_join(df, dendro, join_by(clus, type)) %>%
        mutate(n_ha = 10000 * n / pl_surf,
                ba_ha = 10000 * BA / pl_surf)

###############################################################
# stan model
###############################################################

# Compile the generic Stan model once
sm <- stan_model(here('stan', 'multiple_models.stan'))

# Define model formulas
formulas <- list(
  m01    = ~ 1 + n_ha,
  m02    = ~ 1 + log(n_ha + 1),
  m03    = ~ 1 + sqrt(n_ha),
  m04    = ~ 1 + ba_ha,
  m05    = ~ 1 + log(ba_ha + 1),
  m06    = ~ 1 + sqrt(ba_ha),
  m07    = ~ 1 + n_ha + ba_ha,
  m08    = ~ 1 + log(n_ha + 1) + log(ba_ha + 1),
  m09    = ~ 1 + sqrt(n_ha) + sqrt(ba_ha),
  m10    = ~ 1 + log(n_ha + 1) + sqrt(ba_ha),
  m11    = ~ 1 + sqrt(n_ha) + log(ba_ha + 1),
  # models with mean diameter (dg)
  m12    = ~ 1 + dg,
  m13    = ~ 1 + log(dg),
  m14    = ~ 1 + sqrt(dg),
  m15    = ~ 1 + n_ha + dg,
  m16    = ~ 1 + ba_ha + dg,
  m17    = ~ 1 + n_ha + ba_ha + dg,
  m18    = ~ 1 + log(n_ha + 1) + log(dg),
  m19    = ~ 1 + log(ba_ha + 1) + log(dg),
  m20    = ~ 1 + log(n_ha + 1) + log(ba_ha + 1) + log(dg),
  m21    = ~ 1 + sqrt(n_ha) + sqrt(dg),
  m22    = ~ 1 + sqrt(ba_ha) + sqrt(dg),
  m23    = ~ 1 + sqrt(n_ha) + sqrt(ba_ha) + sqrt(dg),
  m24    = ~ 1 + log(n_ha + 1) + sqrt(ba_ha) + log(dg)
)

###############################################################
###############################################################
###############################################################

# 1. Create prediction grids for different model types
# For models with only n_ha and ba_ha (original models m01-m11)
pred_data_grid_2d <- expand.grid(
  n_ha = 0:160,
  ba_ha = 0:30
)

# For models with dg, create a more manageable 3D grid
# Use a subset of values to keep computational load reasonable
pred_data_grid_3d <- expand.grid(
  n_ha = seq(0, 160, by = 5),    # every 5 trees/ha
  ba_ha = seq(0, 30, by = 1),    # every 1 m²/ha
  dg = seq(15, 105, by = 5)      # every 5 cm diameter
)

# For models with only dg (m12-m14), create a simple 1D grid
pred_data_grid_dg <- data.frame(
  dg = seq(15, 105, by = 1)      # every 1 cm diameter
)

# Initialize a list to store fitted models
fits <- list()

# 2. Loop through each model to fit and get predictions from Stan
for (model_name in names(formulas)) {
  f <- formulas[[model_name]]

  # Prepare original data for Stan
  X_obs <- model.matrix(f, data = df)
  data_list_obs <- list(
    N = nrow(df),
    K = ncol(X_obs),
    X = X_obs,
    cover = df$cover_ellipse
  )

  # Select appropriate prediction grid based on model variables
  # Check which variables are in the formula
  formula_vars <- all.vars(f)

  if ("dg" %in% formula_vars && ("n_ha" %in% formula_vars || "ba_ha" %in% formula_vars)) {
    # Models with dg + other variables: use 3D grid
    current_pred_grid <- pred_data_grid_3d
  } else if ("dg" %in% formula_vars) {
    # Models with only dg: use 1D dg grid
    current_pred_grid <- pred_data_grid_dg
  } else {
    # Models with only n_ha and/or ba_ha: use 2D grid
    current_pred_grid <- pred_data_grid_2d
  }

  # Prepare prediction data for Stan
  X_pred <- model.matrix(f, data = current_pred_grid)
  
  # Ensure K (number of predictors) is consistent between X_obs and X_pred
  # This is important if some formulas might result in different numbers of columns
  # due to factors/interactions not present in pred_data_grid but present in df.
  # If K_pred is different from K, you might need to adjust the Stan model
  # or ensure pred_data_grid covers all levels/combinations to match K.
  # For the given formulas, K should be consistent.
  if (ncol(X_pred) != ncol(X_obs)) {
    stop(paste("Mismatch in number of predictors (K) for model", model_name, 
               "between observed data and prediction data. K_obs:", ncol(X_obs), 
               "K_pred:", ncol(X_pred)))
  }

  # create data list for prediction
  data_list_pred <- list(
    N_pred = nrow(X_pred),
    X_pred = X_pred
  )

  # Combine observed and prediction data for Stan
  full_data_list <- c(data_list_obs, data_list_pred)

  # Fit the model with the combined data
  current_fit <- rstan::sampling(
    sm,
    data    = full_data_list,
    # iter    = 2000,
    # chains  = 4,
    # control = list(adapt_delta = 0.95)
  )
  fits[[model_name]] <- current_fit
}

###############################################################
# save model and formated calibration data
###############################################################

# save fits
saveRDS(fits, here('temp', 'multiple_models.rds'))

# save formated calibration data
saveRDS(df, here('./temp/multiple_calib_data.rds'))

# save prediction data grids
pred_grids <- list(
  grid_2d = pred_data_grid_2d,
  grid_3d = pred_data_grid_3d,
  grid_dg = pred_data_grid_dg
)
saveRDS(pred_grids, here('./temp/multiple_pred_data.rds'))