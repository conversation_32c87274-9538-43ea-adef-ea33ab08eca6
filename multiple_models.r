###############################################################
# initialisation
###############################################################

# clean up environment
rm(list = ls())

# load packages
library(dplyr)
library(tidyr)
library(terra)
library(rstan)
options(mc.cores = parallel::detectCores())
library(ineq)
library(shinystan)
library(here)
library(ggplot2)
library(sf)
library(ggforce)

# load function to extract chain quantiles
source(here('R/tree_cover.r'))

# load tree data
tree <- readRDS(here('./data/treeDB.RDS')) %>%
        filter(dbh >= 10) #%>% # TODO: carefull! it removes trees with no dbh and htot which might affect the final results (?)
        # filter(htot >= 5)

# load plot map
map <- vect(here('./data/sampling.shp'))

###############################################################
# calculate tree cover
###############################################################

# calculate [m]ean [r]adius
tree <- tree %>%
        mutate(mr = rowMeans(across(cN:cNO), na.rm = TRUE))

# TODO: try with new coord ?
# TODO: manage trees with no crowns (their crowns could be modeled?: check their number)
# TODO: manage trees with no coordinates...
tree <- tree %>%
        filter(!is.na(mr)) %>%
        filter(mr>0) %>%
        filter(!is.na(long), !is.na(lat))

# Function to fill missing radii with mean of adjacent directions
fill_missing_radii <- function(df) {
  df %>%
    mutate(
      cN  = if_else(is.na(cN),
                    rowMeans(across(c(cNO, cNE)), na.rm = TRUE),
                    cN),
      cE  = if_else(is.na(cE),
                    rowMeans(across(c(cNE, cSE)), na.rm = TRUE),
                    cE),
      cS  = if_else(is.na(cS),
                    rowMeans(across(c(cSE, cSO)), na.rm = TRUE),
                    cS),
      cO  = if_else(is.na(cO),
                    rowMeans(across(c(cSO, cNO)), na.rm = TRUE),
                    cO),
      cNE = if_else(is.na(cNE),
                    rowMeans(across(c(cN,  cE)), na.rm = TRUE),
                    cNE),
      cSE = if_else(is.na(cSE),
                    rowMeans(across(c(cS,  cE)), na.rm = TRUE),
                    cSE),
      cSO = if_else(is.na(cSO),
                    rowMeans(across(c(cS,  cO)), na.rm = TRUE),
                    cSO),
      cNO = if_else(is.na(cNO),
                    rowMeans(across(c(cN,  cO)), na.rm = TRUE),
                    cNO)
    )
}

# Apply the function twice to fill missing radii
tree <- fill_missing_radii(tree)
tree <- fill_missing_radii(tree)

# calculate tree cover
df <- tree_cover(tree, map)

###############################################################
# calculate dendro variables
###############################################################

dendro <- tree %>%
        mutate(ba = pi*((dbh/100)^2)/4,
                dbh2 = dbh^2) %>%
        group_by(clus, type) %>%
        summarise(n = n(),
                  BA = sum(ba),
                  mean_dbh = mean(dbh),
                  med_dbh = median(dbh),
                  dg = sqrt( sum(dbh2) / sum(n) ),
                  gini = Gini(dbh))

# merge dendro and cover data
df <- full_join(df, dendro, join_by(clus, type)) %>%
        mutate(n_ha = 10000 * n / pl_surf,
                ba_ha = 10000 * BA / pl_surf)

###############################################################
# stan model
###############################################################

# Compile the generic Stan model once
sm <- stan_model(here('stan', 'multiple_models.stan'))

# Define model formulas
formulas <- list(
  m01    = ~ 1 + n_ha,
  m02    = ~ 1 + log(n_ha + 1),
  m03    = ~ 1 + sqrt(n_ha),
  m04    = ~ 1 + ba_ha,
  m05    = ~ 1 + log(ba_ha + 1),
  m06    = ~ 1 + sqrt(ba_ha),
  m07    = ~ 1 + n_ha + ba_ha,
  m08    = ~ 1 + log(n_ha + 1) + log(ba_ha + 1),
  m09    = ~ 1 + sqrt(n_ha) + sqrt(ba_ha),
  m10    = ~ 1 + log(n_ha + 1) + sqrt(ba_ha),
  m11    = ~ 1 + sqrt(n_ha) + log(ba_ha + 1)
  # other variables?
  # m12    = ~ 1 + log(n_ha + 1) + sqrt(ba_ha) + gini
  # m13    = ~ 1 + log(n_ha + 1) + sqrt(ba_ha) + log(dg)
)

###############################################################
###############################################################
###############################################################

# 1. Create a data frame with all combinations of n_ha and ba_ha for prediction
pred_data_grid <- expand.grid(
  n_ha = 0:160,
  ba_ha = 0:30
)

# Initialize a list to store fitted models
fits <- list()

# 2. Loop through each model to fit and get predictions from Stan
for (model_name in names(formulas)) {
  f <- formulas[[model_name]]

  # Prepare original data for Stan
  X_obs <- model.matrix(f, data = df)
  data_list_obs <- list(
    N = nrow(df),
    K = ncol(X_obs),
    X = X_obs,
    cover = df$cover_ellipse
  )

  # Prepare prediction data for Stan
  # Ensure the column names of pred_data_grid match what the formula expects
  X_pred <- model.matrix(f, data = pred_data_grid)
  
  # Ensure K (number of predictors) is consistent between X_obs and X_pred
  # This is important if some formulas might result in different numbers of columns
  # due to factors/interactions not present in pred_data_grid but present in df.
  # If K_pred is different from K, you might need to adjust the Stan model
  # or ensure pred_data_grid covers all levels/combinations to match K.
  # For the given formulas, K should be consistent.
  if (ncol(X_pred) != ncol(X_obs)) {
    stop(paste("Mismatch in number of predictors (K) for model", model_name, 
               "between observed data and prediction data. K_obs:", ncol(X_obs), 
               "K_pred:", ncol(X_pred)))
  }

  # create data list for prediction
  data_list_pred <- list(
    N_pred = nrow(X_pred),
    X_pred = X_pred
  )

  # Combine observed and prediction data for Stan
  full_data_list <- c(data_list_obs, data_list_pred)

  # Fit the model with the combined data
  current_fit <- rstan::sampling(
    sm,
    data    = full_data_list,
    # iter    = 2000,
    # chains  = 4,
    # control = list(adapt_delta = 0.95)
  )
  fits[[model_name]] <- current_fit
}

###############################################################
# save model and formated calibration data
###############################################################

# save fits
saveRDS(fits, here('temp', 'multiple_models.rds'))

# save formated calibration data
saveRDS(df, here('./temp/multiple_calib_data.rds'))

# save prediction data
saveRDS(pred_data_grid, here('./temp/multiple_pred_data.rds'))